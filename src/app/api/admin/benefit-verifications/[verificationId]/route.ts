import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin, isNextRedirectError } from '@/lib/auth'
import { query } from '@/lib/local-db'

/**
 * PUT /api/admin/benefit-verifications/[verificationId]
 * Update benefit verification status (approve/reject)
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ verificationId: string }> }
) {
  try {
    await requireAdmin()
    const { verificationId } = await params
    const body = await request.json()
    const { status } = body

    if (!status || !['approved', 'rejected'].includes(status)) {
      return NextResponse.json(
        { error: 'Status must be either "approved" or "rejected"' },
        { status: 400 }
      )
    }

    // Map admin status values to database values
    const dbStatus = status === 'approved' ? 'confirmed' : 'disputed'

    // Check if verification exists
    const existingResult = await query(
      'SELECT id FROM benefit_verifications WHERE id = $1',
      [verificationId]
    )

    if (existingResult.rows.length === 0) {
      return NextResponse.json(
        { error: 'Benefit verification not found' },
        { status: 404 }
      )
    }

    // Update verification status
    const result = await query(
      `UPDATE benefit_verifications
       SET status = $1
       WHERE id = $2
       RETURNING *`,
      [dbStatus, verificationId]
    )

    return NextResponse.json({
      success: true,
      verification: result.rows[0]
    })

  } catch (error) {
    console.error('Error updating benefit verification:', error)

    // Handle authentication and authorization errors properly
    if (isNextRedirectError(error)) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }
    if (error instanceof Error && error.message === 'Admin access required') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      )
    }

    return NextResponse.json(
      { error: 'Failed to update benefit verification' },
      { status: 500 }
    )
  }
}

/**
 * GET /api/admin/benefit-verifications/[verificationId]
 * Get specific benefit verification details
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ verificationId: string }> }
) {
  try {
    await requireAdmin()
    const { verificationId } = await params

    const result = await query(
      `SELECT 
        bv.*,
        u.email as user_email,
        u.first_name as user_first_name,
        u.last_name as user_last_name,
        c.name as company_name,
        c.id as company_id,
        b.name as benefit_name,
        b.id as benefit_id,
        cb.id as company_benefit_id
       FROM benefit_verifications bv
       JOIN users u ON bv.user_id = u.id
       JOIN company_benefits cb ON bv.company_benefit_id = cb.id
       JOIN companies c ON cb.company_id = c.id
       JOIN benefits b ON cb.benefit_id = b.id
       WHERE bv.id = $1`,
      [verificationId]
    )

    if (result.rows.length === 0) {
      return NextResponse.json(
        { error: 'Benefit verification not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(result.rows[0])

  } catch (error) {
    console.error('Error retrieving benefit verification:', error)

    // Handle authentication and authorization errors properly
    if (isNextRedirectError(error)) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }
    if (error instanceof Error && error.message === 'Admin access required') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      )
    }

    return NextResponse.json(
      { error: 'Failed to retrieve benefit verification' },
      { status: 500 }
    )
  }
}
