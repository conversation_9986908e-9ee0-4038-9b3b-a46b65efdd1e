{"name": "benefitlens", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev --turbopack", "dev:no-turbo": "next dev", "build": "next build", "start": "next start", "lint": "eslint .", "dev:setup": "./scripts/dev-setup.sh", "dev:start": "docker-compose up -d && npm run dev", "dev:stop": "docker-compose down", "dev:reset": "docker-compose down -v && docker-compose up -d", "dev:logs": "docker-compose logs -f", "dev:container:build": "docker-compose -f docker-compose.dev.yml build", "dev:container:start": "docker-compose -f docker-compose.dev.yml --env-file .env.local up -d", "dev:container:stop": "docker-compose -f docker-compose.dev.yml down", "dev:container:reset": "docker-compose -f docker-compose.dev.yml down -v && docker-compose -f docker-compose.dev.yml --env-file .env.local up -d --build", "dev:container:logs": "docker-compose -f docker-compose.dev.yml logs -f", "dev:container:rebuild": "docker-compose -f docker-compose.dev.yml down && docker-compose -f docker-compose.dev.yml build --no-cache && docker-compose -f docker-compose.dev.yml --env-file .env.local up -d", "dev:container:restore": "./scripts/restore-backup.sh", "dev:container:start-with-backup": "npm run dev:container:start && sleep 15 && npm run dev:container:restore", "dev:container": "docker-compose -f docker-compose.dev.yml --env-file .env.local up -d --build", "test:email": "node scripts/test-email.js", "import:german-benefits": "node scripts/import-german-benefits.js", "import:german-benefits:dry-run": "node scripts/import-german-benefits.js --dry-run", "assign:german-benefits": "node scripts/assign-german-benefits.js", "assign:german-benefits:dry-run": "node scripts/assign-german-benefits.js --dry-run", "import:data": "node scripts/import-data.js", "import:demo-companies": "node scripts/import-data.js --type=companies --file=data/demo-companies.csv", "import:demo-companies:dry-run": "node scripts/import-data.js --type=companies --file=data/demo-companies.csv --dry-run", "import:demo-benefits": "node scripts/import-data.js --type=benefits --file=data/demo-benefits.json", "import:demo-benefits:dry-run": "node scripts/import-data.js --type=benefits --file=data/demo-benefits.json --dry-run", "import:demo-benefits-csv": "node scripts/import-data.js --type=benefits --file=data/demo-benefits.csv", "import:demo-benefits-csv:dry-run": "node scripts/import-data.js --type=benefits --file=data/demo-benefits.csv --dry-run", "health:check": "curl -f http://localhost:3000/api/health || exit 1", "health:simple": "curl -f http://localhost:3000/api/health/simple || exit 1", "validate:env": "npx tsx src/lib/env-validation.ts", "logs:tail": "docker-compose logs -f", "db:cli": "docker exec -it benefitlens-postgres psql -U benefitlens_user -d benefitlens", "db:backup": "docker exec benefitlens-postgres pg_dump -U benefitlens_user -d benefitlens > backups/backup_$(date +%Y%m%d_%H%M%S).sql", "db:restore": "docker exec -i benefitlens-postgres psql -U benefitlens_user -d benefitlens < backups/latest.sql", "db:reset-user-data": "docker exec -i benefitlens-postgres psql -U benefitlens_user -d benefitlens < database/reset_user_data.sql", "test": "vitest", "test:unit": "vitest --config vitest.config.ts", "test:integration": "vitest --config vitest.integration.config.ts", "test:all": "npm run test:unit", "test:full": "npm run test:unit && npm run test:integration && npm run test:e2e", "test:e2e": "playwright test --config=playwright-conservative.config.ts src/__tests__/e2e/essential-tests.spec.ts", "test:e2e:full": "playwright test --config=playwright-conservative.config.ts", "test:e2e:full:no-rate-limit": "playwright test --config=playwright-no-rate-limit.config.ts", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "test:comprehensive": "./scripts/run-all-tests.sh", "test:ci": "npm run test:unit -- --run && npm run test:integration -- --run && npm run test:e2e", "cache:cleanup": "npx tsx scripts/session-cleanup.js", "cache:stats": "npx tsx scripts/cache-stats.js", "cache:refresh": "npx tsx scripts/cache-refresh.js", "cache:warmup": "npx tsx scripts/cache-warmup.js", "system:status": "npx tsx scripts/system-status.js", "schema:diff": "./scripts/schema-diff.sh", "schema:check": "./scripts/simple-schema-check.sh", "schema:update": "docker exec benefitlens-postgres pg_dump -U benefitlens_user -d benefitlens --schema-only --no-owner --no-privileges > database/schema.sql", "schema:backup": "cp database/schema.sql database/schema.sql.backup.$(date +%Y%m%d_%H%M%S)", "migration:status": "./scripts/migration-status.sh", "migration:cleanup": "./scripts/migration-cleanup.sh"}, "dependencies": {"@hello-pangea/dnd": "^18.0.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-select": "^2.2.5", "@types/bcryptjs": "^2.4.6", "@types/nodemailer": "^6.4.17", "@types/pg": "^8.15.5", "autoprefixer": "^10.4.21", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "country-state-city": "^3.2.1", "csv-parser": "^3.2.0", "lucide-react": "^0.539.0", "next": "^15.4.6", "node-fetch": "^3.3.2", "nodemailer": "^7.0.5", "pg": "^8.16.3", "react": "19.1.1", "react-dom": "19.1.1", "tailwind-merge": "^3.3.1", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@playwright/test": "^1.55.0", "@tailwindcss/postcss": "^4.1.12", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.3.4", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "dotenv": "^17.2.1", "eslint": "^9", "eslint-config-next": "^15.4.6", "jsdom": "^26.1.0", "playwright": "^1.55.0", "tailwindcss": "^4", "tsx": "^4.20.3", "typescript": "^5.9.2", "vitest": "^3.2.4"}}